.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.settings-header {
  background-color: var(--primary-color, #007bff);
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.settings-back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.settings-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.settings-content {
  padding: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.settings-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-card:last-child {
  margin-bottom: 2rem;
}

.settings-card-content {
  padding: 1.5rem;
}

.settings-card-content.center {
  text-align: center;
}

.settings-section-title {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: var(--primary-color, #007bff);
}

.settings-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 1rem;
}

.qr-container {
  padding: 1rem;
  background-color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #ccc;
  font-size: 4rem;
}

.code-container {
  text-align: center;
  margin-top: 1rem;
}

.code-label {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.code-text {
  font-size: 1.25rem;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  color: var(--primary-color, #007bff);
}

.code-description {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.4;
}

.settings-list-item {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.settings-list-item:last-child {
  border-bottom: none;
}

.settings-list-item:hover {
  background-color: #f8f9fa;
}

.settings-list-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: var(--primary-color, #007bff);
  width: 24px;
  text-align: center;
}

.settings-list-content {
  flex: 1;
}

.settings-list-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #333;
}

.settings-list-description {
  font-size: 0.875rem;
  color: #666;
}

.settings-list-right {
  margin-left: 1rem;
}

.settings-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.settings-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.settings-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.settings-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .settings-slider {
  background-color: var(--primary-color, #007bff);
}

input:checked + .settings-slider:before {
  transform: translateX(26px);
}

.settings-chevron {
  color: #666;
  font-size: 1.25rem;
}

@media (max-width: 768px) {
  .settings-content {
    padding: 0.5rem;
  }
  
  .settings-card-content {
    padding: 1rem;
  }
  
  .qr-placeholder {
    width: 150px;
    height: 150px;
    font-size: 3rem;
  }
}
