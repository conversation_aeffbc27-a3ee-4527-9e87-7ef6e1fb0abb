import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QRCode from 'qrcode.react';
import './settingsScreen.css';

// Mock hooks and contexts for web version
const useAuth = () => ({
  user: {
    userCode: 'USER123456',
    role: 'patient'
  }
});

const getThemeForRole = (role) => ({
  colors: {
    primary: role === 'doctor' ? '#2196F3' : role === 'patient' ? '#4CAF50' : '#007bff'
  }
});

const SettingsScreen = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  const primaryColor = theme.colors.primary;

  // Settings state
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('English');

  // Toggle functions
  const toggleNotifications = () => setNotifications(!notifications);
  const toggleDarkMode = () => setDarkMode(!darkMode);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleChangePassword = () => {
    navigate('/change-password');
  };

  const SettingsListItem = ({ icon, title, description, rightComponent, onClick }) => (
    <div className="settings-list-item" onClick={onClick}>
      <div className="settings-list-icon">
        {icon}
      </div>
      <div className="settings-list-content">
        <div className="settings-list-title">{title}</div>
        {description && <div className="settings-list-description">{description}</div>}
      </div>
      <div className="settings-list-right">
        {rightComponent}
      </div>
    </div>
  );

  const Switch = ({ checked, onChange }) => (
    <label className="settings-switch">
      <input type="checkbox" checked={checked} onChange={onChange} />
      <span className="settings-slider"></span>
    </label>
  );

  return (
    <div className="settings-container" style={{ '--primary-color': primaryColor }}>
      {/* Header */}
      <div className="settings-header">
        <button className="settings-back-button" onClick={handleGoBack}>
          ←
        </button>
        <h1 className="settings-title">Settings</h1>
      </div>

      {/* Content */}
      <div className="settings-content">
        {/* QR Code Section */}
        <div className="settings-card">
          <div className="settings-card-content center">
            <h2 className="settings-section-title">My QR Code</h2>
            <div className="settings-divider"></div>

            <div className="qr-container">
              {user?.userCode ? (
                <QRCode
                  value={user.userCode}
                  size={200}
                  fgColor={primaryColor}
                  bgColor="white"
                  level="M"
                />
              ) : (
                <div className="qr-placeholder">
                  📱
                </div>
              )}
            </div>

            <div className="code-container">
              <div className="code-label">Your Unique Code</div>
              <div className="code-text">{user?.userCode || 'CODE NOT FOUND'}</div>
              <div className="code-description">
                Share this code with healthcare providers to connect with them
              </div>
            </div>
          </div>
        </div>

        {/* General Settings */}
        <div className="settings-card">
          <div className="settings-card-content">
            <h2 className="settings-section-title">General Settings</h2>
            <div className="settings-divider"></div>

            <SettingsListItem
              icon="🔔"
              title="Notifications"
              description="Enable push notifications"
              rightComponent={<Switch checked={notifications} onChange={toggleNotifications} />}
            />

            <SettingsListItem
              icon="🌙"
              title="Dark Mode"
              description="Enable dark theme"
              rightComponent={<Switch checked={darkMode} onChange={toggleDarkMode} />}
            />

            <SettingsListItem
              icon="🌍"
              title="Language"
              description={language}
              rightComponent={<span className="settings-chevron">›</span>}
              onClick={() => {/* Open language selection */}}
            />
          </div>
        </div>

        {/* Privacy & Security */}
        <div className="settings-card">
          <div className="settings-card-content">
            <h2 className="settings-section-title">Privacy & Security</h2>
            <div className="settings-divider"></div>

            <SettingsListItem
              icon="🔒"
              title="Change Password"
              rightComponent={<span className="settings-chevron">›</span>}
              onClick={handleChangePassword}
            />

            <SettingsListItem
              icon="🛡️"
              title="Privacy Policy"
              rightComponent={<span className="settings-chevron">›</span>}
              onClick={() => {/* Open privacy policy */}}
            />

            <SettingsListItem
              icon="📄"
              title="Terms of Service"
              rightComponent={<span className="settings-chevron">›</span>}
              onClick={() => {/* Open terms of service */}}
            />
          </div>
        </div>

        {/* About */}
        <div className="settings-card">
          <div className="settings-card-content">
            <h2 className="settings-section-title">About</h2>
            <div className="settings-divider"></div>

            <SettingsListItem
              icon="ℹ️"
              title="App Version"
              description="1.0.0"
            />

            <SettingsListItem
              icon="❓"
              title="Contact Support"
              rightComponent={<span className="settings-chevron">›</span>}
              onClick={() => {/* Open support contact */}}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsScreen;
